import bpy
from bpy.types import Operator
from bpy.props import EnumProperty, StringProperty

class SDF_OT_TreeAddItem(Operator):
    """Add an item to the SDF tree"""
    bl_idname = "sdf.tree_add_item"
    bl_label = "Add SDF Item"
    bl_options = {'REGISTER', 'UNDO'}
    
    item_type: EnumProperty(
        name="Type",
        items=[
            ('SPHERE', "Sphere", "Add sphere primitive"),
            ('BOX', "Box", "Add box primitive"),
            ('CYLINDER', "Cylinder", "Add cylinder primitive"),
            ('TORUS', "Torus", "Add torus primitive"),
            ('UNION', "Union", "Add union operation"),
            ('SUBTRACT', "Subtract", "Add subtraction operation"),
            ('INTERSECT', "Intersect", "Add intersection operation"),
            ('TRANSLATE', "Translate", "Add translation transform"),
            ('ROTATE', "Rotate", "Add rotation transform"),
            ('SCALE', "Scale", "Add scale transform"),
        ]
    )
    
    def execute(self, context):
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            self.report({'ERROR'}, "SDF tree not initialized. Please reinstall the addon.")
            print("SDF Tree: sdf_tree property not found on scene")
            return {'CANCELLED'}

        print(f"SDF Tree: Adding {self.item_type} to tree")
        
        # Add the item
        tree = scene.sdf_tree
        parent_index = tree.active_index if tree.items else -1
        
        # For operations, use the active item as parent
        if self.item_type in ['UNION', 'SUBTRACT', 'INTERSECT']:
            if tree.items and tree.active_index < len(tree.items):
                parent_index = tree.active_index
            else:
                parent_index = -1
        else:
            parent_index = -1
        
        item = tree.add_item(self.item_type, parent_index=parent_index)
        tree.active_index = len(tree.items) - 1
        
        # Trigger shader update
        self._update_shader(context)
        
        self.report({'INFO'}, f"Added {self.item_type.lower()}")
        return {'FINISHED'}
    
    def _update_shader(self, context):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

class SDF_OT_TreeRemoveItem(Operator):
    """Remove selected item from SDF tree"""
    bl_idname = "sdf.tree_remove_item"
    bl_label = "Remove SDF Item"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return {'CANCELLED'}
        
        tree = scene.sdf_tree
        if tree.items and 0 <= tree.active_index < len(tree.items):
            tree.remove_item(tree.active_index)
            self._update_shader(context)
            self.report({'INFO'}, "Removed SDF item")
        
        return {'FINISHED'}
    
    def _update_shader(self, context):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

class SDF_OT_TreeMoveItem(Operator):
    """Move item up or down in the SDF tree"""
    bl_idname = "sdf.tree_move_item"
    bl_label = "Move SDF Item"
    bl_options = {'REGISTER', 'UNDO'}
    
    direction: EnumProperty(
        items=[
            ('UP', "Up", "Move item up"),
            ('DOWN', "Down", "Move item down"),
        ]
    )
    
    def execute(self, context):
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return {'CANCELLED'}
        
        tree = scene.sdf_tree
        if not tree.items:
            return {'CANCELLED'}
        
        current_index = tree.active_index
        if self.direction == 'UP' and current_index > 0:
            tree.move_item(current_index, current_index - 1)
            tree.active_index = current_index - 1
        elif self.direction == 'DOWN' and current_index < len(tree.items) - 1:
            tree.move_item(current_index, current_index + 1)
            tree.active_index = current_index + 1
        
        self._update_shader(context)
        return {'FINISHED'}
    
    def _update_shader(self, context):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

class SDF_OT_TreeDuplicateItem(Operator):
    """Duplicate selected item in SDF tree"""
    bl_idname = "sdf.tree_duplicate_item"
    bl_label = "Duplicate SDF Item"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return {'CANCELLED'}
        
        tree = scene.sdf_tree
        if tree.items and 0 <= tree.active_index < len(tree.items):
            source_item = tree.items[tree.active_index]
            
            # Create new item
            new_item = tree.add_item(source_item.item_type, 
                                   name=source_item.name + " Copy",
                                   parent_index=source_item.parent_index)
            
            # Copy properties
            new_item.radius = source_item.radius
            new_item.size = source_item.size
            new_item.height = source_item.height
            new_item.major_radius = source_item.major_radius
            new_item.minor_radius = source_item.minor_radius
            new_item.location = source_item.location
            new_item.rotation = source_item.rotation
            new_item.scale = source_item.scale
            new_item.smooth_radius = source_item.smooth_radius
            
            tree.active_index = len(tree.items) - 1
            self._update_shader(context)
            self.report({'INFO'}, "Duplicated SDF item")
        
        return {'FINISHED'}
    
    def _update_shader(self, context):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

class SDF_OT_TreeClear(Operator):
    """Clear all items from SDF tree"""
    bl_idname = "sdf.tree_clear"
    bl_label = "Clear SDF Tree"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        if not hasattr(scene, 'sdf_tree'):
            return {'CANCELLED'}
        
        tree = scene.sdf_tree
        tree.items.clear()
        tree.active_index = 0
        
        self._update_shader(context)
        self.report({'INFO'}, "Cleared SDF tree")
        return {'FINISHED'}
    
    def _update_shader(self, context):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
        except:
            pass

# Quick add operators for common primitives
class SDF_OT_TreeAddSphere(Operator):
    """Add sphere to SDF tree"""
    bl_idname = "sdf.tree_add_sphere"
    bl_label = "Add Sphere"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        bpy.ops.sdf.tree_add_item(item_type='SPHERE')
        return {'FINISHED'}

class SDF_OT_TreeAddBox(Operator):
    """Add box to SDF tree"""
    bl_idname = "sdf.tree_add_box"
    bl_label = "Add Box"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        bpy.ops.sdf.tree_add_item(item_type='BOX')
        return {'FINISHED'}

class SDF_OT_TreeAddCylinder(Operator):
    """Add cylinder to SDF tree"""
    bl_idname = "sdf.tree_add_cylinder"
    bl_label = "Add Cylinder"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        bpy.ops.sdf.tree_add_item(item_type='CYLINDER')
        return {'FINISHED'}

class SDF_OT_TreeAddTorus(Operator):
    """Add torus to SDF tree"""
    bl_idname = "sdf.tree_add_torus"
    bl_label = "Add Torus"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        bpy.ops.sdf.tree_add_item(item_type='TORUS')
        return {'FINISHED'}

class SDF_OT_TreeInitialize(Operator):
    """Initialize SDF Tree System"""
    bl_idname = "sdf.tree_initialize"
    bl_label = "Initialize SDF Tree"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        scene = context.scene

        # Ensure the tree property exists
        if not hasattr(scene, 'sdf_tree'):
            self.report({'ERROR'}, "SDF Tree property not found. Please reinstall the addon.")
            return {'CANCELLED'}

        # Clear any existing items
        scene.sdf_tree.items.clear()
        scene.sdf_tree.active_index = 0

        self.report({'INFO'}, "SDF Tree initialized")
        return {'FINISHED'}

class SDF_OT_UpdateViewport(Operator):
    """Update SDF Viewport Rendering"""
    bl_idname = "sdf.update_viewport"
    bl_label = "Update Viewport"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .shaders import SDFRenderer
            SDFRenderer.refresh_shader()
            self.report({'INFO'}, "Viewport updated")
        except Exception as e:
            self.report({'ERROR'}, f"Failed to update viewport: {e}")
        return {'FINISHED'}

# Register classes
classes = [
    SDF_OT_TreeAddItem,
    SDF_OT_TreeRemoveItem,
    SDF_OT_TreeMoveItem,
    SDF_OT_TreeDuplicateItem,
    SDF_OT_TreeClear,
    SDF_OT_TreeAddSphere,
    SDF_OT_TreeAddBox,
    SDF_OT_TreeAddCylinder,
    SDF_OT_TreeAddTorus,
    SDF_OT_TreeInitialize,
    SDF_OT_UpdateViewport,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
