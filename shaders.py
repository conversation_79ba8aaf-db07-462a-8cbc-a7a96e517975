import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from mathutils import Matrix

# Vertex shader for fullscreen quad
vertex_shader = """
    in vec2 position;
    out vec2 texCoord;

    void main()
    {
        gl_Position = vec4(position, 0.0, 1.0);
        texCoord = position * 0.5 + 0.5;
    }
"""

# Fragment shader template - the sceneSDF function will be injected
fragment_shader_template = """
    uniform vec2 resolution;
    uniform mat4 ViewMatrixInverse;
    uniform vec3 cameraPos;
    uniform float max_steps;
    uniform float max_dist;
    uniform float surface_threshold;
    
    in vec2 texCoord;
    out vec4 fragColor;
    
    // SDF Primitives
    float sdSphere(vec3 p, float r) {
        return length(p) - r;
    }
    
    float sdBox(vec3 p, vec3 b) {
        vec3 q = abs(p) - b;
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
    }
    
    float sdCylinder(vec3 p, float r, float h) {
        vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r, h);
        return min(max(d.x, d.y), 0.0) + length(max(d, 0.0));
    }
    
    float sdTorus(vec3 p, float R, float r) {
        vec2 q = vec2(length(p.xz) - R, p.y);
        return length(q) - r;
    }
    
    // SDF Operations
    float opUnion(float d1, float d2) {
        return min(d1, d2);
    }

    float opSubtraction(float d1, float d2) {
        return max(-d1, d2);
    }

    float opIntersection(float d1, float d2) {
        return max(d1, d2);
    }

    // Smooth operations
    float smin(float a, float b, float k) {
        float h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0);
        return mix(b, a, h) - k * h * (1.0 - h);
    }

    float smax(float a, float b, float k) {
        return -smin(-a, -b, k);
    }
    
    // Scene SDF - This will be generated by the node system
    float sceneSDF(vec3 p) {
        {scene_sdf_code}
    }
    
    // Raymarching function
    float raymarch(vec3 ro, vec3 rd, float max_dist, float max_steps, float surface_threshold) {
        float depth = 0.0;
        
        for (int i = 0; i < int(max_steps); i++) {
            vec3 p = ro + depth * rd;
            float dist = sceneSDF(p);
            
            if (dist < surface_threshold) {
                return depth;
            }
            
            depth += dist;
            
            if (depth >= max_dist) {
                return -1.0;
            }
        }
        
        return -1.0;
    }
    
    // Calculate normal using central differences
    vec3 calculateNormal(vec3 p) {
        const float h = 0.0001;
        const vec2 k = vec2(1.0, -1.0) * h;
        
        return normalize(k.xyy * sceneSDF(p + k.xyy) +
                        k.yyx * sceneSDF(p + k.yyx) +
                        k.yxy * sceneSDF(p + k.yxy) +
                        k.xxx * sceneSDF(p + k.xxx));
    }
    
    // Main shader function
    void main() {
        // Calculate ray direction
        vec2 uv = (2.0 * texCoord - 1.0) * vec2(resolution.x / resolution.y, 1.0);
        
        // Create ray from camera
        vec3 ro = cameraPos;
        vec4 rd_view = vec4(uv, -1.0, 0.0);
        vec3 rd = normalize((ViewMatrixInverse * rd_view).xyz);
        
        // Raymarch the scene
        float t = raymarch(ro, rd, max_dist, max_steps, surface_threshold);
        
        // Shade based on hit distance
        if (t > 0.0) {
            // Calculate hit position and normal
            vec3 hitPos = ro + t * rd;
            vec3 normal = calculateNormal(hitPos);
            
            // Simple shading with normal
            vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
            float diff = max(dot(normal, lightDir), 0.1);
            
            // Set fragment color
            fragColor = vec4(vec3(diff), 1.0);
        } else {
            // Background color (sky blue)
            fragColor = vec4(0.5, 0.7, 1.0, 1.0);
        }
    }
"""

def _draw_callback_helper():
    """Wrapper to call the renderer's draw callback"""
    try:
        context = bpy.context
        if context:
            SDFRenderer._draw_callback(context)
    except Exception as e:
        print(f"SDF Renderer: Error in draw callback helper: {e}")
        # Disable renderer on persistent errors to prevent crashes
        SDFRenderer.disable()


class SDFRenderer:
    """Handles SDF viewport rendering"""
    _handle = None
    _shader = None
    _batch = None
    _enabled = False
    _glsl_cache = None
    _timer_handle = None
    _current_settings = {
        'max_steps': 100,
        'max_dist': 100.0,
        'surface_threshold': 0.001,
    }
    
    @classmethod
    def is_enabled(cls):
        """Check if the renderer is enabled"""
        return cls._enabled
    
    @classmethod
    def _is_handler_valid(cls):
        """Check if the draw handler is valid"""
        return cls._handle is not None and cls._enabled
    
    @classmethod
    def update_settings(cls, **kwargs):
        """Update renderer settings
        
        Args:
            **kwargs: Settings to update (max_steps, max_dist, surface_threshold)
        """
        cls._current_settings.update(kwargs)
        
        # Recreate shader if needed
        if cls._shader is not None:
            cls._create_shader_program()
    
    @classmethod
    def enable(cls):
        """Enable the SDF renderer"""
        if cls._enabled:
            return

        try:
            # Create shader if it doesn't exist
            if cls._shader is None:
                if not cls._create_shader_program():
                    raise RuntimeError("Failed to create shader program")

            # Create batch if it doesn't exist
            if cls._batch is None and cls._shader is not None:
                vertices = [
                    (-1, -1), (1, -1), (1, 1),  # First triangle
                    (-1, -1), (1, 1), (-1, 1)   # Second triangle
                ]
                cls._batch = batch_for_shader(
                    cls._shader, 'TRIS',
                    {"position": vertices}
                )

            # Add draw handler
            if cls._handle is None:
                cls._handle = bpy.types.SpaceView3D.draw_handler_add(
                    _draw_callback_helper, (), 'WINDOW', 'POST_VIEW'
                )

            # Add timer for periodic updates
            if cls._timer_handle is None:
                cls._timer_handle = bpy.app.timers.register(cls._timer_update, first_interval=0.1, persistent=True)

            cls._enabled = True
            print("SDF Renderer enabled")
        except Exception as e:
            print(f"SDF Renderer: Error enabling renderer: {e}")
            cls.disable() # Ensure clean state on failure
    
    @classmethod
    def disable(cls):
        if not cls._enabled:
            return
        cls._enabled = False

        # Remove draw handler
        if cls._handle:
            try:
                bpy.types.SpaceView3D.draw_handler_remove(cls._handle, 'WINDOW')
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing draw handler: {e}")
            finally:
                cls._handle = None

        # Remove timer
        if cls._timer_handle:
            try:
                bpy.app.timers.unregister(cls._timer_handle)
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing timer: {e}")
            finally:
                cls._timer_handle = None
    
    @classmethod
    def refresh_shader(cls):
        """Refresh the shader with the current tree system"""
        print("SDF Renderer: Refreshing shader for tree system")
        cls._create_shader_program()

        # Force viewport redraw
        cls._force_viewport_redraw()

    @classmethod
    def _force_viewport_redraw(cls):
        """Force all 3D viewports to redraw"""
        try:
            import bpy
            # Redraw all areas
            for window in bpy.context.window_manager.windows:
                for area in window.screen.areas:
                    if area.type == 'VIEW_3D':
                        area.tag_redraw()

            # Also trigger a general redraw
            if bpy.context.window_manager:
                bpy.context.window_manager.tag_redraw()

        except Exception as e:
            print(f"SDF Renderer: Error forcing viewport redraw: {e}")

    @classmethod
    def _timer_update(cls):
        """Timer callback for periodic viewport updates"""
        try:
            if cls._enabled and cls._shader:
                # Only redraw if we have a valid context and tree items
                if bpy.context.scene and hasattr(bpy.context.scene, 'sdf_tree'):
                    tree = bpy.context.scene.sdf_tree
                    if tree.items:
                        cls._force_viewport_redraw()

            # Return the interval for next update (0.1 seconds)
            return 0.1

        except Exception as e:
            print(f"SDF Renderer: Error in timer update: {e}")
            # Return None to stop the timer on error
            return None
    
    @classmethod
    def reset(cls):
        """Reset the renderer state"""
        cls.disable()
        cls._shader = None
        cls._batch = None
        cls._enabled = False
        cls._handle = None
        cls._timer_handle = None
        print("SDF Renderer reset")
    
    @classmethod
    def _generate_scene_glsl(cls):
        """Generate GLSL code for the scene SDF from the tree system"""
        try:
            import bpy
            scene = bpy.context.scene
            if hasattr(scene, 'sdf_tree') and scene.sdf_tree.items:
                print("SDF Renderer: Using tree system for GLSL generation")
                glsl_code = scene.sdf_tree.generate_glsl()
                print(f"SDF Renderer: Generated tree GLSL: {glsl_code}")
                return glsl_code
            else:
                print("SDF Renderer: No tree items found, returning empty scene")
                return "return 1000.0;"  # No geometry - empty scene
        except Exception as e:
            print(f"SDF Renderer: Error with tree system: {e}")
            import traceback
            traceback.print_exc()
            return "return 1000.0;"  # Fallback to empty scene
    
    @classmethod
    def _create_shader_program(cls):
        try:
            # Check if GPU module is available
            if not hasattr(gpu, 'types') or not hasattr(gpu.types, 'GPUShader'):
                print("GPU shader support not available")
                return None
            
            # Generate scene GLSL code
            scene_glsl = cls._generate_scene_glsl()
            
            # Create fragment shader with the generated scene code
            fragment_shader = fragment_shader_template.replace("{scene_sdf_code}", scene_glsl)

            print(f"SDF Renderer: Full scene GLSL being used: {scene_glsl}")

            # Create shader
            shader = gpu.types.GPUShader(vertex_shader, fragment_shader)
            cls._shader = shader
            print(f"SDF Renderer: Created shader with scene GLSL: {scene_glsl[:100]}...")
            return shader
        except Exception as e:
            print(f"Shader creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def register(cls):
        if cls._shader is not None:
            return
            
        try:
            # Create shader program
            if not cls._create_shader_program():
                raise Exception("Failed to create shader program")
            
            # Create fullscreen quad vertices
            vertices = [
                (-1, -1), (1, -1), (1, 1),  # First triangle
                (-1, -1), (1, 1), (-1, 1)   # Second triangle
            ]
            
            cls._batch = batch_for_shader(
                cls._shader, 'TRIS',
                {"position": vertices}
            )
            
            # Initial enable
            cls.enable()
            print("SDF Renderer initialized successfully")
            return True
            
        except Exception as e:
            print(f"Failed to initialize SDF renderer: {e}")
            import traceback
            traceback.print_exc()
            cls.unregister()
            return False
    
    @classmethod
    def unregister(cls):
        if cls._handle is not None:
            try:
                bpy.types.SpaceView3D.draw_handler_remove(cls._handle, 'WINDOW')
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing draw handler during unregister: {e}")
            finally:
                cls._handle = None
        
        cls._shader = None
        cls._batch = None
    
    @classmethod
    def _draw_callback(cls, context):
        """Draw callback for viewport rendering"""
        if not cls._shader or not cls._batch or not cls._is_handler_valid():
            print("SDF Renderer: Draw callback skipped - missing shader, batch, or invalid handler")
            return
            
        # Check if context is valid
        if not context or not hasattr(context, 'region') or not context.region:
            print("SDF Renderer: Draw callback skipped - invalid context")
            return
            
        # Get active region
        region = context.region
        if not hasattr(context, 'space_data') or not context.space_data:
            print("SDF Renderer: Draw callback skipped - no space_data")
            return
            
        region3d = context.space_data.region_3d
        
        # Check if we have a valid tree system
        scene = context.scene
        if not hasattr(scene, 'sdf_tree') or not scene.sdf_tree.items:
            print("SDF Renderer: Draw callback skipped - no tree items")
            return

        # Check if viewport rendering is enabled
        if not hasattr(scene, 'sdf') or not scene.sdf.sdf_show_in_viewport:
            print("SDF Renderer: Draw callback skipped - viewport rendering disabled")
            return
            
        try:
            print("SDF Renderer: Drawing frame...")
            
            # Bind shader
            cls._shader.bind()
            
            # Set shader uniforms
            cls._shader.uniform_float("resolution", (region.width, region.height))
            cls._shader.uniform_float("ViewMatrixInverse", region3d.view_matrix.inverted())
            cls._shader.uniform_float("cameraPos", region3d.view_matrix.inverted().translation)
            
            # Set rendering parameters from current settings
            cls._shader.uniform_float("max_steps", float(cls._current_settings['max_steps']))
            cls._shader.uniform_float("max_dist", float(cls._current_settings['max_dist']))
            cls._shader.uniform_float("surface_threshold", float(cls._current_settings['surface_threshold']))
            
            # Draw fullscreen quad
            cls._batch.draw(cls._shader)
            
            print("SDF Renderer: Frame drawn successfully")
            
        except Exception as e:
            print(f"Error in SDF renderer: {e}")
            import traceback
            traceback.print_exc()
            # Disable renderer on error to prevent further crashes
            cls.disable()
            return
            
        finally:
            # Clean up OpenGL state
            gpu.state.blend_set('NONE')
            gpu.state.depth_test_set('NONE')
            
            # Unbind shader if available
            if hasattr(gpu, 'shader'):
                gpu.shader.unbind()
            elif hasattr(gpu, 'shader_from_builtin'):
                gpu.shader.from_builtin('3D_UNIFORM_COLOR').unbind()

def register():
    try:
        SDFRenderer.reset()  # Ensure clean state
        SDFRenderer.register()
    except Exception as e:
        print(f"Failed to register SDF renderer: {e}")
        import traceback
        traceback.print_exc()

def unregister():
    try:
        SDFRenderer.reset()  # Clean shutdown
    except Exception as e:
        print(f"Failed to unregister SDF renderer: {e}")
