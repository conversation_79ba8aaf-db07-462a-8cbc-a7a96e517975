import bpy
from bpy.types import Property<PERSON>roup, UIList, Operator
from bpy.props import (
    StringProperty, 
    EnumProperty, 
    FloatProperty, 
    FloatVectorProperty,
    IntProperty,
    BoolProperty,
    CollectionProperty,
    PointerProperty
)

# SDF Tree Item Types
class SDFTreeItem(PropertyGroup):
    """Individual item in the SDF tree"""
    
    name: StringProperty(
        name="Name",
        default="SDF Item"
    )
    
    item_type: EnumProperty(
        name="Type",
        items=[
            ('SPHERE', "Sphere", "Sphere primitive"),
            ('BOX', "Box", "Box primitive"),
            ('CYLINDER', "Cylinder", "Cylinder primitive"),
            ('TORUS', "Torus", "Torus primitive"),
            ('UNION', "Union", "Union operation"),
            ('SUBTRACT', "Subtract", "Subtraction operation"),
            ('INTERSECT', "Intersect", "Intersection operation"),
            ('TRANSLATE', "Translate", "Translation transform"),
            ('ROTATE', "Rotate", "Rotation transform"),
            ('SCALE', "Scale", "Scale transform"),
        ],
        default='SPHERE'
    )
    
    # Primitive properties
    radius: FloatProperty(name="Radius", default=1.0, min=0.0)
    size: FloatVectorProperty(name="Size", default=(1.0, 1.0, 1.0), size=3)
    height: FloatProperty(name="Height", default=2.0, min=0.0)
    major_radius: FloatProperty(name="Major Radius", default=1.0, min=0.0)
    minor_radius: FloatProperty(name="Minor Radius", default=0.3, min=0.0)
    
    # Transform properties
    location: FloatVectorProperty(name="Location", default=(0.0, 0.0, 0.0), size=3, subtype='TRANSLATION')
    rotation: FloatVectorProperty(name="Rotation", default=(0.0, 0.0, 0.0), size=3, subtype='EULER')
    scale: FloatVectorProperty(name="Scale", default=(1.0, 1.0, 1.0), size=3)
    
    # Operation properties
    smooth_radius: FloatProperty(name="Smooth Radius", default=0.0, min=0.0)
    
    # Tree structure
    parent_index: IntProperty(default=-1)
    indent_level: IntProperty(default=0)
    is_expanded: BoolProperty(default=True)
    is_enabled: BoolProperty(default=True)
    
    def generate_glsl(self, child_code=""):
        """Generate GLSL code for this item"""
        if self.item_type == 'SPHERE':
            return f"length(p - vec3{tuple(self.location)}) - {self.radius}"
        
        elif self.item_type == 'BOX':
            loc = tuple(self.location)
            size = tuple(self.size)
            return f"length(max(abs(p - vec3{loc}) - vec3{size}, 0.0)) + min(max(abs(p - vec3{loc}).x - {size[0]}, max(abs(p - vec3{loc}).y - {size[1]}, abs(p - vec3{loc}).z - {size[2]})), 0.0)"
        
        elif self.item_type == 'CYLINDER':
            loc = tuple(self.location)
            return f"max(length((p - vec3{loc}).xy) - {self.radius}, abs((p - vec3{loc}).z) - {self.height * 0.5})"
        
        elif self.item_type == 'TORUS':
            loc = tuple(self.location)
            return f"length(vec2(length((p - vec3{loc}).xz) - {self.major_radius}, (p - vec3{loc}).y)) - {self.minor_radius}"
        
        elif self.item_type == 'UNION':
            if self.smooth_radius > 0.0:
                return f"smin(a, b, {self.smooth_radius})"
            return "min(a, b)"
        
        elif self.item_type == 'SUBTRACT':
            if self.smooth_radius > 0.0:
                return f"smin(a, -b, {self.smooth_radius})"
            return "max(a, -b)"
        
        elif self.item_type == 'INTERSECT':
            if self.smooth_radius > 0.0:
                return f"smax(a, b, {self.smooth_radius})"
            return "max(a, b)"
        
        elif self.item_type == 'TRANSLATE':
            offset = tuple(self.location)
            return f"sdf(p - vec3{offset})"
        
        return "1000.0"  # Default large distance

# UI List for SDF Tree
class SDF_UL_TreeList(UIList):
    """UI List for SDF tree items"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            # Indentation for hierarchy
            for i in range(item.indent_level):
                layout.label(text="", icon='BLANK1')
            
            # Expand/collapse button for operations
            if item.item_type in ['UNION', 'SUBTRACT', 'INTERSECT']:
                icon = 'DOWNARROW_HLT' if item.is_expanded else 'RIGHTARROW'
                layout.prop(item, "is_expanded", text="", icon=icon, emboss=False)
            else:
                layout.label(text="", icon='BLANK1')
            
            # Enable/disable toggle
            layout.prop(item, "is_enabled", text="", icon='HIDE_OFF' if item.is_enabled else 'HIDE_ON')
            
            # Item icon and name
            icons = {
                'SPHERE': 'MESH_UVSPHERE',
                'BOX': 'MESH_CUBE',
                'CYLINDER': 'MESH_CYLINDER',
                'TORUS': 'MESH_TORUS',
                'UNION': 'SELECT_EXTEND',
                'SUBTRACT': 'SELECT_SUBTRACT',
                'INTERSECT': 'SELECT_INTERSECT',
                'TRANSLATE': 'ORIENTATION_GLOBAL',
                'ROTATE': 'DRIVER_ROTATIONAL_DIFFERENCE',
                'SCALE': 'FULLSCREEN_EXIT'
            }
            
            layout.prop(item, "name", text="", emboss=False, icon=icons.get(item.item_type, 'OBJECT_DATA'))
        
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon='OBJECT_DATA')

# SDF Tree Collection
class SDFTreeCollection(PropertyGroup):
    """Collection of SDF tree items"""
    
    items: CollectionProperty(type=SDFTreeItem)
    active_index: IntProperty(default=0)
    
    def add_item(self, item_type, name=None, parent_index=-1):
        """Add a new item to the tree"""
        item = self.items.add()
        item.item_type = item_type
        item.name = name or item_type.title()
        item.parent_index = parent_index
        
        # Calculate indent level
        if parent_index >= 0 and parent_index < len(self.items) - 1:
            parent = self.items[parent_index]
            item.indent_level = parent.indent_level + 1
        else:
            item.indent_level = 0
        
        return item
    
    def remove_item(self, index):
        """Remove an item and its children"""
        if 0 <= index < len(self.items):
            # Remove children first
            self._remove_children(index)
            # Remove the item itself
            self.items.remove(index)
            # Update active index
            if self.active_index >= len(self.items):
                self.active_index = max(0, len(self.items) - 1)
    
    def _remove_children(self, parent_index):
        """Recursively remove children of an item"""
        i = len(self.items) - 1
        while i >= 0:
            if i < len(self.items) and self.items[i].parent_index == parent_index:
                self._remove_children(i)
                self.items.remove(i)
            i -= 1
    
    def move_item(self, from_index, to_index):
        """Move an item in the tree"""
        if 0 <= from_index < len(self.items) and 0 <= to_index < len(self.items):
            self.items.move(from_index, to_index)
    
    def ensure_default_item(self):
        """Ensure the tree has at least one item"""
        if not self.items:
            print("SDF Tree: Adding default sphere to empty tree")
            self.add_item('SPHERE', name="Default Sphere")
            self.active_index = 0

    def generate_glsl(self):
        """Generate GLSL code for the entire tree"""
        # Ensure we have at least one item
        self.ensure_default_item()

        if not self.items:
            return "return 1000.0;"  # No geometry

        # Get all enabled root items (items with no parent)
        root_items = [item for item in self.items if item.is_enabled and item.parent_index == -1]

        if not root_items:
            return "return 1000.0;"

        if len(root_items) == 1:
            # Single root item
            glsl = root_items[0].generate_glsl()
            return f"return {glsl};"
        else:
            # Multiple root items - union them together
            glsl_parts = []
            for i, item in enumerate(root_items):
                glsl_parts.append(f"float d{i} = {item.generate_glsl()};")

            # Union all distances
            union_expr = "d0"
            for i in range(1, len(root_items)):
                union_expr = f"min({union_expr}, d{i})"

            glsl_code = "\n".join(glsl_parts) + f"\nreturn {union_expr};"
            return glsl_code

# Register classes
classes = [
    SDFTreeItem,
    SDF_UL_TreeList,
    SDFTreeCollection,
]

@bpy.app.handlers.persistent
def initialize_tree_on_load(dummy):
    """Initialize SDF tree when file loads"""
    try:
        for scene in bpy.data.scenes:
            if hasattr(scene, 'sdf_tree'):
                # Tree property exists, ensure it's accessible
                tree = scene.sdf_tree

                # If tree is empty, add a default sphere
                if not tree.items:
                    print(f"SDF Tree: Adding default sphere to scene {scene.name}")
                    tree.add_item('SPHERE', name="Default Sphere")
                    tree.active_index = 0

                print(f"SDF Tree initialized for scene: {scene.name} with {len(tree.items)} items")
    except Exception as e:
        print(f"Error initializing SDF tree: {e}")

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

    # Add load handler
    if initialize_tree_on_load not in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.append(initialize_tree_on_load)

def unregister():
    # Remove load handler
    if initialize_tree_on_load in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.remove(initialize_tree_on_load)

    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
